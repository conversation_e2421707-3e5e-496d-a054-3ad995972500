import 'package:flutter/material.dart';

class TagChip extends StatelessWidget {
  final String tag;
  final double? fontSize;
  final EdgeInsetsGeometry? padding;
  final double? borderRadius;

  const TagChip({
    super.key,
    required this.tag,
    this.fontSize = 13,
    this.padding = const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
    this.borderRadius = 18,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: padding,
      decoration: BoxDecoration(
        color: _getColorByFirstLetter(tag),
        borderRadius: BorderRadius.circular(borderRadius!),
      ),
      child: Text(
        tag,
        style: TextStyle(
          color: Colors.white,
          fontSize: fontSize,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  /// 根据标签首字母分配颜色
  Color _getColorByFirstLetter(String text) {
    if (text.isEmpty) return Colors.grey.withOpacity(0.8);
    
    final firstChar = text.toLowerCase()[0];
    
    switch (firstChar) {
      case 'a':
      case 'b':
        return Colors.red.withOpacity(0.8);
      case 'c':
      case 'd':
        return Colors.orange.withOpacity(0.8);
      case 'e':
      case 'f':
        return Colors.amber.withOpacity(0.8);
      case 'g':
      case 'h':
        return Colors.green.withOpacity(0.8);
      case 'i':
      case 'j':
        return Colors.teal.withOpacity(0.8);
      case 'k':
      case 'l':
        return Colors.cyan.withOpacity(0.8);
      case 'm':
      case 'n':
        return Colors.blue.withOpacity(0.8);
      case 'o':
      case 'p':
        return Colors.indigo.withOpacity(0.8);
      case 'q':
      case 'r':
        return Colors.purple.withOpacity(0.8);
      case 's':
      case 't':
        return Colors.pink.withOpacity(0.8);
      case 'u':
      case 'v':
        return Colors.deepPurple.withOpacity(0.8);
      case 'w':
      case 'x':
        return Colors.brown.withOpacity(0.8);
      case 'y':
      case 'z':
        return Colors.blueGrey.withOpacity(0.8);
      default:
        return Colors.grey.withOpacity(0.8);
    }
  }
}

/// 标签列表组件
class TagChipList extends StatelessWidget {
  final List<String> tags;
  final int? maxTags;
  final double spacing;
  final double runSpacing;
  final double? fontSize;
  final EdgeInsetsGeometry? padding;
  final double? borderRadius;

  const TagChipList({
    super.key,
    required this.tags,
    this.maxTags,
    this.spacing = 8,
    this.runSpacing = 10,
    this.fontSize = 13,
    this.padding = const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
    this.borderRadius = 18,
  });

  @override
  Widget build(BuildContext context) {
    final displayTags = maxTags != null ? tags.take(maxTags!).toList() : tags;
    
    return Wrap(
      spacing: spacing,
      runSpacing: runSpacing,
      children: displayTags.map((tag) {
        return TagChip(
          tag: tag,
          fontSize: fontSize,
          padding: padding,
          borderRadius: borderRadius,
        );
      }).toList(),
    );
  }
} 